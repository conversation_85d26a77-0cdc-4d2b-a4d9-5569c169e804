import React, { useEffect, useLayoutEffect } from "react";
import { PeekBox, PeekDivider, PeekSwipeableDrawer } from "@piiqtechnologies/ui-components";
import { useAnalytics } from "@utils";

const drawerBleeding = 72;

interface ClickableEdgeDrawerProps {
  children: React.ReactNode;
  DrawerBleedingContent: React.FC;
  eventIdentification?: string;
}

export default function ClickableEdgeDrawer({
  children,
  DrawerBleedingContent,
  eventIdentification,
}: ClickableEdgeDrawerProps) {
  const [open, setOpen] = React.useState(false);
  const swipeableDrawerRef = React.useRef(null);
  const layoutViewportRef = React.useRef(null);
  const analytics = useAnalytics();

  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen);

    if (newOpen) {
      analytics?.track(`${eventIdentification}_opened`);
    }
  };

  function viewportHandler() {
    const viewport = window.visualViewport as VisualViewport;
    const layoutViewport = layoutViewportRef.current as unknown as HTMLDivElement;
    const bottomBar = swipeableDrawerRef.current as unknown as HTMLDivElement;
    // Since the bar is position: fixed we need to offset it by the visual
    // viewport's offset from the layout viewport origin.
    const offsetX = viewport.offsetLeft;
    const offsetY = viewport.height - layoutViewport.getBoundingClientRect().height + viewport.offsetTop;
    // You could also do this by setting style.left and style.top if you
    // use width: 100% instead.
    bottomBar.style.transform = "translate(" + offsetX + "px," + offsetY + "px) " + "scale(" + 1 / viewport.scale + ")";
  }

  useEffect(() => {
    const viewport = window.visualViewport as VisualViewport;
    viewport.addEventListener("scroll", viewportHandler);
    viewport.addEventListener("resize", viewportHandler);

    return () => {
      viewport.removeEventListener("resize", viewportHandler);
      viewport.removeEventListener("scroll", viewportHandler);
    };
  }, []);

  return (
    <>
      <PeekSwipeableDrawer
        anchor="bottom"
        open={open}
        onClose={toggleDrawer(false)}
        onOpen={toggleDrawer(true)}
        swipeAreaWidth={drawerBleeding}
        disableSwipeToOpen={false}
        SwipeAreaProps={{
          onClick: toggleDrawer(true),
        }}
        ModalProps={{
          keepMounted: true,
          ref: swipeableDrawerRef,
        }}
        data-testid="swipeable-edge-drawer"
      >
        <PeekBox
          sx={{
            position: "absolute",
            top: -drawerBleeding,
            visibility: "visible",
            borderTopLeftRadius: 12,
            borderTopRightRadius: 12,
            right: 0,
            left: 0,
            backgroundColor: "common.white",
            boxShadow: "0px -2px 4px #ADADAD21",
          }}
        >
          <Puller />
          <DrawerBleedingContent />
        </PeekBox>
        <PeekDivider light orientation="horizontal" flexItem sx={{ height: "5px" }} />
        <PeekBox
          sx={{
            p: 2,
            height: "100%",
            overflow: "auto",
            backgroundColor: "common.white",
          }}
        >
          {children}
        </PeekBox>
      </PeekSwipeableDrawer>
      <PeekBox ref={layoutViewportRef} position="fixed" height="100%" width="100%" visibility="hidden" />
    </>
  );
}
