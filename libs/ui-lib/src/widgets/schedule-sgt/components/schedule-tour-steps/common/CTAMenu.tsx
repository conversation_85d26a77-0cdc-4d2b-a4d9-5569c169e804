import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>eek<PERSON><PERSON><PERSON>utton,
  <PERSON>eek<PERSON>ist,
  PeekListItemButton,
  PeekListItemIcon,
  PeekListItemText,
  useTheme,
} from "@piiqtechnologies/ui-components";
import {
  Headset,
  MapTrifold,
  BookOpen,
  Question,
  ArrowSquareOut,
  MessageCircle,
  ChevronLeft,
} from "@piiqtechnologies/svg-icons";
import React, { MemoExoticComponent, SVGProps, useState } from "react";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import SwipeableEdgeDrawer from "@widgets/components/swipeable-drawer/SwipeableEdgeDrawer";
import { TabLeavingWarning } from "@widgets/components/tab-leaving-warning";

import { getTourLabel, useAnalytics } from "@utils";
import { CASAChatType, CASAChat as ChatWidget, PromptType } from "@widgets/components/casa-chat";
import { CASA_FEATURES, Community, CommunityTours, Prospect } from "@types";

interface CTAMenuProps {
  hideCommunityGuide?: boolean;
}

enum CASAErrorLink {
  CALL_AGENT = "call_agent",
}

export interface CTAMenuItemProps {
  onClick: () => void;
  primaryText: string;
  Icon: MemoExoticComponent<(props: SVGProps<SVGSVGElement>) => JSX.Element>;
}

function* promptGenerator(prospect: Prospect) {
  yield [
    PromptType.TEXT,
    `Hello${
      prospect?.firstName ? ` ${prospect?.firstName}` : ""
    }, I hope you are enjoying your tour. What can I help you with today?`,
  ];
  return;
}

const CTAMenuItem: React.FC<CTAMenuItemProps> = ({ onClick, primaryText, Icon }) => {
  return (
    <PeekListItemButton
      data-testid="cta-menu-item"
      sx={{ px: 0, py: 2, gap: 1.5, ":hover": { bgcolor: "transparent" } }}
      onClick={onClick}
    >
      <PeekListItemIcon color="primary">
        <Icon height={19} width={19} />
      </PeekListItemIcon>
      <PeekListItemText
        primary={primaryText}
        primaryTypographyProps={{
          color: "primary",
          variant: "h3",
        }}
      />
    </PeekListItemButton>
  );
};

enum WARNING_TYPE {
  COMMUNITY_MAP = "COMMUNITY_MAP",
  COMMUNITY_GUIDE = "COMMUNITY_GUIDE",
}

const CTAMenu = ({ hideCommunityGuide }: CTAMenuProps) => {
  const { tourDetails, community } = useScheduleTour();
  const theme = useTheme();
  const analytics = useAnalytics();
  const user = analytics?.user();
  const [warning, setWarning] = useState<WARNING_TYPE | null>(null);
  const [openChat, setOpenChat] = useState(false);
  const isCASAEnabled = community.features?.includes(CASA_FEATURES.SGT_CASA);
  const prospect = tourDetails?.verifiedPerson?.prospect as Prospect;
  const sessionId = analytics?.getState()?.context?.sessionId;

  const toggleChat = () => {
    setOpenChat((openChat) => !openChat);
    analytics?.track(`casa_chat_${openChat ? "closed" : "opened"}`);
  };

  const onCallAgentClick = () => {
    analytics?.track("cta_call_agent");
    document.location.href = `tel:${community?.sgtSupportPhoneNumber}`;
  };

  const handleChatURL = (href: string) => {
    // Add additional conditions for other URLs as needed
    analytics?.track("casa_chat_link_clicked", {
      href,
    });
  };

  const handleChatLinks = (event: React.MouseEvent) => {
    if (event.target instanceof HTMLAnchorElement) {
      // Handle the click on <a> tags
      const href = event.target.getAttribute("href") as string;
      handleChatURL(href);
    }
    if (event.target instanceof HTMLHeadingElement) {
      const linkId = event.target.getAttribute("itemid");
      switch (linkId) {
        case CASAErrorLink.CALL_AGENT:
          event.preventDefault();
          onCallAgentClick();
          break;
        default:
          handleChatURL(linkId as string);
          window.open(linkId, "_blank");
          break;
      }
    }
  };

  const onWarningConfirm = () => {
    let communityURL = "";
    switch (warning) {
      case WARNING_TYPE.COMMUNITY_MAP:
        communityURL = community?.mapLink || "";
        break;
      case WARNING_TYPE.COMMUNITY_GUIDE:
        communityURL = community?.guideLink || "";
        break;
    }
    analytics?.track(`cta_${warning?.toLowerCase()}`);
    window.open(communityURL, "_blank");
    setWarning(null);
  };

  return (
    <>
      {!!warning && (
        <TabLeavingWarning
          onClose={() => setWarning(null)}
          message={`Please remember to return to this tab to continue your ${getTourLabel(community, CommunityTours.SGT)}.`}
          ConfirmButton={() => (
            <PeekButton
              fullWidth
              onClick={onWarningConfirm}
              startIcon={<ArrowSquareOut height={17} width={17} />}
              variant="contained"
              color="primary"
            >
              {`View Community ${warning === WARNING_TYPE.COMMUNITY_MAP ? "Map" : "Guide"}`}
            </PeekButton>
          )}
        />
      )}
      <SwipeableEdgeDrawer
        eventIdentification={(!openChat && "additional_information_drawer") || "casa_chat_drawer"}
        DrawerBleedingContent={() => (
          <PeekButton
            fullWidth
            sx={{
              height: "auto !important",
              px: 2,
              pb: 2.5,
              pt: 2.8,
              ...theme.typography.h3,
              fontWeight: "fontWeightMedium",
              justifyContent: "flex-start",
            }}
            startIcon={
              (!openChat && <Question height={19} width={19} />) || (
                <PeekIconButton bordered={false} onClick={toggleChat}>
                  <ChevronLeft height={19} width={19} />
                </PeekIconButton>
              )
            }
          >
            {(!openChat && "Additional Information") || "Chat with us"}
          </PeekButton>
        )}
      >
        {(openChat && (
          <ChatWidget
            anonymousId={user?.anonymousId as string}
            prospectId={user?.userId as string}
            communityId={community?._id as string}
            spaceId={tourDetails?._id as string}
            sessionId={sessionId as string}
            prompts={promptGenerator(prospect)}
            sxProps={{ p: 0, mr: -2.8 }}
            type={CASAChatType.SGT}
            errorPrompt={[
              {
                type: PromptType.TEXT,
                message:
                  "I apologize for the inconvenience. Our chat service is temporarily unavailable. Please try the following instead",
              },
              {
                type: PromptType.SUGGESTION,
                message: "Speak to an agent",
                linkId: CASAErrorLink.CALL_AGENT,
              },
            ]}
            processChatLink={handleChatLinks}
          />
        )) || (
          <PeekList disablePadding>
            {!isCASAEnabled ? (
              !!community?.sgtSupportPhoneNumber && (
                <CTAMenuItem primaryText="Speak to an agent" onClick={onCallAgentClick} Icon={Headset} />
              )
            ) : (
              <CTAMenuItem primaryText="Chat with us" onClick={toggleChat} Icon={MessageCircle} />
            )}
            {!!community?.mapLink && (
              <CTAMenuItem
                primaryText="View community map"
                onClick={() => setWarning(WARNING_TYPE.COMMUNITY_MAP)}
                Icon={MapTrifold}
              />
            )}
            {!!community?.guideLink && !hideCommunityGuide && (
              <CTAMenuItem
                primaryText="View community guide"
                onClick={() => setWarning(WARNING_TYPE.COMMUNITY_GUIDE)}
                Icon={BookOpen}
              />
            )}
          </PeekList>
        )}
      </SwipeableEdgeDrawer>
    </>
  );
};

export default CTAMenu;
